<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Certificate of Signature</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap');

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      margin: 0;
      padding: 20px;
    }

    .certificate-container {
      width: 794px;
      height: 1123px;
      margin: 0 auto;
      background: white;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      overflow: hidden;
      position: relative;
      background-image: url('/certificate-background.png');
      background-size: cover;
      background-position: center;
    }

    .certificate-overlay {
      background: rgba(255, 255, 255, 0.9);
      height: 100%;
      padding: 48px;
      border-top: 8px solid rgba(56, 189, 248, 0.5);
      display: flex;
      flex-direction: column;
    }

    .certificate-content {
      flex-grow: 1;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
    }

    .header h1 {
      font-family: Georgia, serif;
      font-size: 24px;
      font-weight: bold;
      color: #1e293b;
      margin: 0;
    }

    .verification-badge {
      position: relative;
      width: 96px;
      height: 96px;
      opacity: 0.5;
      margin-top: 24px;
    }

    .badge-outer {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50%;
      background: linear-gradient(135deg, #38bdf8 0%, #7dd3fc 100%);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .badge-inner {
      position: absolute;
      top: 4px;
      left: 4px;
      right: 4px;
      bottom: 4px;
      border-radius: 50%;
      background: white;
    }

    .badge-icon {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #38bdf8;
    }

    .sections {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .section {
      margin-bottom: 16px;
    }

    .section-title {
      font-size: 14px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      color: #475569;
      border-bottom: 1px solid #e2e8f0;
      padding-bottom: 6px;
      margin-bottom: 8px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32px 32px;
      margin-bottom: 12px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .info-icon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      color: #94a3b8;
      flex-shrink: 0;
    }

    .info-content {
      flex: 1;
    }

    .info-label {
      font-size: 10px;
      color: #64748b;
      margin-bottom: 2px;
    }

    .info-value {
      font-size: 12px;
      color: #1e293b;
      font-weight: 500;
    }

    .signature-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 32px;
      margin-top: 12px;
    }

    .signature-details {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .signature-display {
      background: rgba(255, 255, 255, 0.8);
      padding: 8px;
      border-radius: 6px;
      border: 1px solid rgba(226, 232, 240, 0.7);
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .signature-image {
      max-width: 250px;
      max-height: 100px;
      mix-blend-mode: darken;
    }

    .signature-text {
      font-family: 'Dancing Script', cursive;
      font-size: 24px;
      color: #1e293b;
    }

    .verification-list {
      display: flex;
      flex-direction: column;
      gap: 6px;
    }

    .verification-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
    }

    .verification-icon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      color: #64748b;
      flex-shrink: 0;
    }

    .verification-text {
      font-size: 12px;
      color: #334155;
      line-height: 1.4;
    }

    .device-section {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid rgba(226, 232, 240, 0.8);
    }

    .device-title {
      font-size: 10px;
      font-weight: bold;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      color: #64748b;
      margin-bottom: 8px;
    }

    .footer {
      padding-top: 16px;
      margin-top: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .footer-logo {
      width: 80px;
      height: 22px;
    }

    .footer-text {
      text-align: right;
    }

    .footer-small {
      font-size: 10px;
      color: #64748b;
    }

    .footer-brand {
      font-weight: 600;
      font-size: 12px;
      color: #334155;
      margin-top: 4px;
    }
  </style>
</head>
<body>
  <div class="certificate-container">
    <div class="certificate-overlay">
      <div class="certificate-content">
        <div class="header">
          <div>
            <h1>Certificate of Signature</h1>
          </div>
          <div class="verification-badge">
            <div class="badge-outer"></div>
            <div class="badge-inner"></div>
            <div class="badge-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                <path d="m9 12 2 2 4-4"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="sections">
          <div class="section">
            <h2 class="section-title">Document Information</h2>
            <div class="info-grid">
              <div class="info-item">
                <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path>
                  <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                  <rect width="4" height="6" x="2" y="12" rx="2"></rect>
                  <path d="M10 12h2v6"></path>
                  <path d="M10 18h4"></path>
                </svg>
                <div class="info-content">
                  <div class="info-label">Document ID</div>
                  <div class="info-value"><%= @document&.id || 'doc_a7b3c9f2-1e8d-4b4c-8f9a-5d6e7f8g9h0i' %></div>
                </div>
              </div>
              <div class="info-item">
                <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4"></path>
                  <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                  <rect width="4" height="6" x="2" y="12" rx="2"></rect>
                  <path d="M10 12h2v6"></path>
                  <path d="M10 18h4"></path>
                </svg>
                <div class="info-content">
                  <div class="info-label">Reference Number</div>
                  <div class="info-value">GQXBE-QUYB4-BOWXD-BVTFS</div>
                </div>
              </div>
            </div>
          </div>

          <div class="section">
            <h2 class="section-title">Signature Details</h2>
            <div class="signature-grid">
              <div class="signature-details">
                <div class="info-item">
                  <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                  </svg>
                  <div class="info-content">
                    <div class="info-label">Email Address</div>
                    <div class="info-value"><%= @patient&.email || '<EMAIL>' %></div>
                  </div>
                </div>
                <div class="info-item">
                  <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 10a2 2 0 0 0-2 2c0 1.02-.1 2.51-.26 4"></path>
                    <path d="M14 13.12c0 2.38 0 6.38-1 8.88"></path>
                    <path d="M17.29 21.02c.12-.6.43-2.3.5-3.02"></path>
                    <path d="M2 12a10 10 0 0 1 18-6"></path>
                    <path d="M2 16h.01"></path>
                    <path d="M21.8 16c.2-2 .131-5.354 0-6"></path>
                    <path d="M5 19.5C5.5 18 6 15 6 12a6 6 0 0 1 .34-2"></path>
                    <path d="M8.65 22c.21-.66.45-1.32.57-2"></path>
                    <path d="M9 6.8a6 6 0 0 1 9 5.2v2"></path>
                  </svg>
                  <div class="info-content">
                    <div class="info-label">IP Address</div>
                    <div class="info-value"><%= @signature_request&.ip_address || '*************' %></div>
                  </div>
                </div>
                <div class="info-item">
                  <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                    <path d="M2 12h20"></path>
                  </svg>
                  <div class="info-content">
                    <div class="info-label">Location</div>
                    <div class="info-value"><%= @patient&.city || 'Enfield' %>, <%= @patient&.country || 'United Kingdom' %></div>
                  </div>
                </div>
              </div>
              <div class="signature-display">
                <% if @signature_request&.signature_image&.attached? %>
                  <img alt="Digital Signature" width="250" height="100" class="signature-image" src="data:image/png;base64,<%= Base64.strict_encode64(@signature_request.signature_image.download) %>">
                <% else %>
                  <div class="signature-text"><%= @signature_request&.signed_name || 'John Doe' %></div>
                <% end %>
              </div>
            </div>
          </div>

          <div class="section">
            <h2 class="section-title">Verification Methods</h2>
            <div class="verification-list">
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                  <path d="m9 12 2 2 4-4"></path>
                </svg>
                <div class="verification-text">The document was sent to <strong>Testing Practitioner</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                <div class="verification-text">A secure 6 digit code was sent to <strong>+447772705073</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                  <polyline points="10 17 15 12 10 7"></polyline>
                  <line x1="15" x2="3" y1="12" y2="12"></line>
                </svg>
                <div class="verification-text">The code was entered into the <strong>Your Practice</strong> patient portal on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="14" height="20" x="5" y="2" rx="2" ry="2"></rect>
                  <path d="M12 18h.01"></path>
                </svg>
                <div class="verification-text">Patient logged in via mobile 2 factor authentication on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
                <div class="verification-text">The Document was viewed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                </svg>
                <div class="verification-text">The document was signed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                </svg>
                <div class="verification-text">Copy of document was emailed to <strong><%= @patient&.email || '<EMAIL>' %></strong></div>
              </div>
              <div class="verification-item">
                <svg class="verification-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
                <div class="verification-text">Link to document was sent via SMS to <strong>+447772705073</strong></div>
              </div>
            </div>

            <div class="device-section">
              <h3 class="device-title">Device Information</h3>
              <div class="info-grid">
                <div class="info-item">
                  <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="20" height="14" x="2" y="3" rx="2"></rect>
                    <line x1="8" x2="16" y1="21" y2="21"></line>
                    <line x1="12" x2="12" y1="17" y2="21"></line>
                  </svg>
                  <div class="info-content">
                    <div class="info-label">Operating System</div>
                    <div class="info-value"><%= @signature_request&.os_name || 'macOS' %> <%= @signature_request&.os_version || 'Sonoma 14.5' %></div>
                  </div>
                </div>
                <div class="info-item">
                  <svg class="info-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path>
                    <path d="M2 12h20"></path>
                  </svg>
                  <div class="info-content">
                    <div class="info-label">Browser</div>
                    <div class="info-value"><%= @signature_request&.browser_name || 'Chrome' %> <%= @signature_request&.browser_version || '126.0.6478.127' %></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        <img alt="UPOD Medical Logo" class="footer-logo" src="/upod-logo.png">
        <div class="footer-text">
          <div class="footer-small">This certificate confirms the successful completion and digital signing of the associated document.</div>
          <div class="footer-brand">Signature confirmed and verified using UPOD Medical practice management software</div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
