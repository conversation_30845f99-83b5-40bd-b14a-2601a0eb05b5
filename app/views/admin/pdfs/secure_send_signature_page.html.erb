<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Signature Certificate</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
    
    body {
      font-family: 'Poppins', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-image: url('<%= @background_image_url %>');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      min-height: 100vh;
    }
    
    .page-border {
      border: 3px solid #28a745;
      min-height: calc(100vh - 40px);
      padding: 20px;
      position: relative;
    }
    
    .header {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .header h1 {
      font-size: 18px;
      font-weight: bold;
      margin: 15px 0;
    }
    
    .header .doc-ref {
      font-size: 12px;
      margin: 15px 0 30px 0;
    }
    
    .signing-section {
      margin-bottom: 30px;
    }
    
    .signing-section h2 {
      font-size: 12px;
      color: #888888;
      margin-bottom: 10px;
    }
    
    .signer-box {
      border: 1px solid #000000;
      height: 100px;
      display: flex;
      position: relative;
    }
    
    .avatar-section {
      width: 100px;
      height: 100px;
      border-right: 1px solid #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 3px;
    }
    
    .avatar-section img {
      width: 95px;
      height: 95px;
      object-fit: cover;
    }
    
    .info-section {
      flex: 1;
      padding: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .info-section .name {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .info-section .email-label {
      font-size: 10px;
      color: #888888;
      margin-bottom: 2px;
    }
    
    .info-section .email {
      font-size: 10px;
      font-weight: bold;
    }
    
    .signature-section {
      width: 150px;
      height: 100px;
      border-left: 1px solid #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
    }
    
    .signature-image {
      max-width: 130px;
      max-height: 80px;
    }
    
    .signature-text {
      font-family: 'Dancing Script', cursive;
      font-size: 16px;
      text-align: center;
    }
    
    .merge-tags-section {
      margin-top: 40px;
    }
    
    .merge-tags-section h2 {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .merge-tags-table {
      width: 100%;
      border-collapse: collapse;
    }
    
    .merge-tags-table th,
    .merge-tags-table td {
      padding: 5px;
      border-bottom: 0.5px solid #cccccc;
      text-align: left;
      font-size: 12px;
    }
    
    .merge-tags-table th {
      font-weight: bold;
      background-color: #f5f5f5;
    }
    
    .footer {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="page-border">
    <div class="header">
      <h1>Signature Certificate</h1>
      <div class="doc-ref">Document Ref.: <%= @document.id %></div>
    </div>
    
    <div class="signing-section">
      <h2>Document signed by:</h2>
      <div class="signer-box">
        <div class="avatar-section">
          <% if @patient.image.attached? %>
            <img src="data:image/<%= @patient.image.blob.content_type.split('/').last %>;base64,<%= Base64.strict_encode64(@patient.image.download) %>" alt="Patient Avatar">
          <% else %>
            <img src="<%= @default_avatar_url %>" alt="Default Avatar">
          <% end %>
        </div>
        
        <div class="info-section">
          <div class="name"><%= @patient.full_name %></div>
          <div class="email-label">Verified E-mail:</div>
          <div class="email"><%= @patient.email %></div>
        </div>
        
        <div class="signature-section">
          <% if @signature_fields.any? %>
            <% signature_field = @signature_fields.select { |field| field[:type] == 'signature' }.first %>
            <% if signature_field %>
              <% if @signature_request.signature_image.attached? %>
                <img src="data:image/png;base64,<%= Base64.strict_encode64(@signature_request.signature_image.download) %>" 
                     alt="Signature" class="signature-image">
              <% else %>
                <div class="signature-text"><%= @signature_request.signed_name %></div>
              <% end %>
            <% end %>
          <% else %>
            <% if @signature_request.signature_image.attached? %>
              <img src="data:image/png;base64,<%= Base64.strict_encode64(@signature_request.signature_image.download) %>" 
                   alt="Signature" class="signature-image">
            <% else %>
              <div class="signature-text"><%= @signature_request.signed_name %></div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
    
    <% if @merge_tags.any? %>
      <div class="merge-tags-section">
        <h2>Document Data</h2>
        <table class="merge-tags-table">
          <thead>
            <tr>
              <th style="width: 20%;">Field</th>
              <th style="width: 30%;">Value</th>
              <th style="width: 50%;">Context</th>
            </tr>
          </thead>
          <tbody>
            <% @merge_tags.each do |tag, data| %>
              <tr>
                <td><%= tag %></td>
                <td><%= data[:value] %></td>
                <td><%= data[:context] %></td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    <% end %>
    
    <div class="footer">
      This document has been electronically signed
    </div>
  </div>
</body>
</html>
