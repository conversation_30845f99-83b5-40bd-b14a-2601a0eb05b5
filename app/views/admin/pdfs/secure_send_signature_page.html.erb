<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Certificate of Signature</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&display=swap');

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 40px 20px;
      color: #333;
    }

    .certificate {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }

    .certificate::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .header {
      padding: 40px 40px 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .header h1 {
      font-size: 28px;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
    }

    .verification-badge {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .verification-badge::after {
      content: '✓';
      font-size: 32px;
      color: #4299e1;
      font-weight: bold;
    }

    .content {
      padding: 0 40px 40px;
    }

    .section {
      margin-bottom: 32px;
    }

    .section-title {
      font-size: 12px;
      font-weight: 600;
      color: #718096;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 12px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
      margin-bottom: 32px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    .info-icon {
      width: 16px;
      height: 16px;
      margin-top: 2px;
      opacity: 0.6;
    }

    .info-content {
      flex: 1;
    }

    .info-label {
      font-size: 11px;
      color: #718096;
      margin-bottom: 2px;
    }

    .info-value {
      font-size: 13px;
      color: #2d3748;
      font-weight: 500;
    }

    .signature-container {
      background: #f7fafc;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .signature-details {
      flex: 1;
    }

    .signature-email {
      font-size: 13px;
      color: #2d3748;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .signature-ip {
      font-size: 11px;
      color: #718096;
    }

    .signature-location {
      font-size: 11px;
      color: #718096;
    }

    .signature-display {
      text-align: right;
      max-width: 200px;
    }

    .signature-image {
      max-width: 180px;
      max-height: 60px;
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      padding: 8px;
      background: white;
    }

    .signature-text {
      font-family: 'Dancing Script', cursive;
      font-size: 24px;
      color: #2d3748;
      font-weight: 400;
    }

    .verification-list {
      list-style: none;
      padding: 0;
    }

    .verification-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;
      font-size: 13px;
      color: #4a5568;
    }

    .verification-icon {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #48bb78;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 10px;
      font-weight: bold;
      flex-shrink: 0;
    }

    .device-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
    }

    .footer {
      background: #f7fafc;
      padding: 24px 40px;
      text-align: center;
      border-top: 1px solid #e2e8f0;
    }

    .footer-text {
      font-size: 11px;
      color: #718096;
      margin-bottom: 8px;
    }

    .footer-brand {
      font-size: 13px;
      color: #4299e1;
      font-weight: 600;
    }

    .upod-logo {
      color: #4299e1;
      font-weight: 700;
      font-size: 16px;
      letter-spacing: -0.5px;
    }
  </style>
</head>
<body>
  <div class="certificate">
    <div class="header">
      <h1>Certificate of Signature</h1>
      <div class="verification-badge"></div>
    </div>

    <div class="content">
      <div class="section">
        <div class="section-title">Document Information</div>
        <div class="info-grid">
          <div class="info-item">
            <svg class="info-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"/>
            </svg>
            <div class="info-content">
              <div class="info-label">Document ID</div>
              <div class="info-value"><%= @document&.id || 'doc_47b3c9f2-1e8d-4b4c-8f9a-5d6e7f8g9h0i' %></div>
            </div>
          </div>
          <div class="info-item">
            <svg class="info-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
              <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
            </svg>
            <div class="info-content">
              <div class="info-label">Reference Number</div>
              <div class="info-value">GQXBE-QUY84-BQWXD-BYTF5</div>
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Signature Details</div>
        <div class="signature-container">
          <div class="signature-details">
            <div class="signature-email"><%= @patient&.email || '<EMAIL>' %></div>
            <div class="signature-ip">IP Address: <%= @signature_request&.ip_address || '*************' %></div>
            <div class="signature-location">Location: <%= @patient&.city || 'Enfield' %>, <%= @patient&.country || 'United Kingdom' %></div>
          </div>
          <div class="signature-display">
            <% if @signature_request&.signature_image&.attached? %>
              <img src="data:image/png;base64,<%= Base64.strict_encode64(@signature_request.signature_image.download) %>"
                   alt="Signature" class="signature-image">
            <% else %>
              <div class="signature-text"><%= @signature_request&.signed_name || 'Sample Signature' %></div>
            <% end %>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Verification Methods</div>
        <ul class="verification-list">
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>The document was sent to <strong>Testing Practitioner</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>A secure 6 digit code was sent to <strong>+447772705073</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>The code was entered into the <strong>Your Practice</strong> patient portal on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>Patient logged in via mobile 2 factor authentication on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>The Document was viewed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>The document was signed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>Copy of document was emailed to <strong><%= @patient&.email || '<EMAIL>' %></strong></span>
          </li>
          <li class="verification-item">
            <div class="verification-icon">✓</div>
            <span>Link to document was sent via SMS to <strong>+447772705073</strong></span>
          </li>
        </ul>
      </div>

      <div class="section">
        <div class="section-title">Device Information</div>
        <div class="device-grid">
          <div class="info-item">
            <svg class="info-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"/>
            </svg>
            <div class="info-content">
              <div class="info-label">Operating System</div>
              <div class="info-value"><%= @signature_request&.os_name || 'macOS' %> <%= @signature_request&.os_version || 'Sonoma 14.5' %></div>
            </div>
          </div>
          <div class="info-item">
            <svg class="info-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"/>
            </svg>
            <div class="info-content">
              <div class="info-label">Browser</div>
              <div class="info-value"><%= @signature_request&.browser_name || 'Chrome' %> <%= @signature_request&.browser_version || '126.0.6478.127' %></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <div class="footer-text">This certificate confirms the successful completion and digital signing of the associated document.</div>
      <div class="footer-brand">Signature confirmed and verified using <span class="upod-logo">UPOD</span> Medical practice management software</div>
    </div>
  </div>
</body>
</html>
