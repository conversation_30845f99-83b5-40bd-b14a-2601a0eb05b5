<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Certificate of Signature</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            background-color: white;
            color: #333;
            -webkit-print-color-adjust: exact;
        }

        .certificate {
            width: 100%;
            min-height: 100vh;
            margin: 0;
            padding: 40px 40px 120px 40px;
            background-color: white;
            border: 8px solid #87CEEB;
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
        }

        .header {
            border-bottom: 2px solid #ddd;
            padding-bottom: 15px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .header h1 {
            font-size: 24px;
            color: #2c3e50;
            font-family: "Times New Roman", serif;
            font-weight: bold;
            float: left;
            margin: 0;
        }

        .shield {
            float: right;
            width: 60px;
            height: 60px;
            background-color: #87CEEB;
            border-radius: 30px;
            text-align: center;
            line-height: 60px;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .section {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .verification-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .verification-content {
            flex: 1;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .info-grid {
            width: 100%;
        }

        .info-row {
            margin-bottom: 10px;
            width: 48%;
            display: inline-block;
            vertical-align: top;
        }

        .info-row-full {
            margin-bottom: 10px;
            width: 100%;
            display: block;
        }

        .info-label {
            font-size: 9px;
            color: #888;
            text-transform: uppercase;
            font-weight: bold;
            display: block;
        }

        .info-value {
            font-size: 11px;
            color: #333;
            margin-top: 2px;
            display: block;
            word-break: break-all;
        }

        .signature-section {
            overflow: hidden;
        }

        .signature-details {
            width: 65%;
            float: left;
        }

        .signature-box {
            width: 30%;
            float: right;
            border: 2px solid #ddd;
            background-color: #f9f9f9;
            padding: 15px;
            text-align: center;
            height: 80px;
            line-height: 50px;
        }

        .signature-image {
            max-width: 100%;
            max-height: 50px;
            vertical-align: middle;
        }

        .signature-text {
            font-family: cursive;
            font-size: 16px;
            color: #333;
        }

        .verification-list {
            list-style: none;
            padding: 0;
        }

        .verification-item {
            margin-bottom: 8px;
            font-size: 11px;
            color: #555;
            padding-left: 15px;
            position: relative;
            line-height: 1.4;
            page-break-inside: avoid;
        }

        .verification-item:before {
            content: "✓";
            position: absolute;
            left: 0;
            top: 0;
            color: #87CEEB;
            font-weight: bold;
            font-size: 12px;
        }

        .device-section {
            border-top: 2px solid #87CEEB;
            padding-top: 15px;
            margin-top: 25px;
            page-break-before: auto;
        }

        .audit-trail-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #87CEEB;
            page-break-before: auto;
        }

        .device-title {
            font-size: 9px;
            font-weight: bold;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .footer {
            border-top: 2px solid #87CEEB;
            padding-top: 15px;
            margin-top: auto;
            overflow: hidden;
            position: absolute;
            bottom: 40px;
            left: 40px;
            right: 40px;
            background-color: white;
        }

        .footer-left {
            float: left;
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
        }

        .footer-right {
            float: right;
            text-align: right;
            width: 300px;
        }

        .footer-text {
            font-size: 8px;
            color: #666;
            margin-bottom: 5px;
        }

        .footer-confirm {
            font-size: 10px;
            font-weight: bold;
            color: #333;
        }

        /* WickedPDF specific fixes */
        @media print {
            .certificate {
                width: 100%;
                margin: 0;
                padding: 30px;
                border: 8px solid #87CEEB;
                min-height: 100vh;
            }

            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }

            .footer {
                bottom: 30px;
                left: 30px;
                right: 30px;
            }
        }

        /* Clearfix for older rendering engines */
        .clearfix:after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body>
    <div class="certificate">
        <div class="content-wrapper">
            <div class="header clearfix">
                <h1>Certificate of Signature</h1>
                <div class="shield">🛡</div>
            </div>

            <div class="section">
                <div class="section-title">Document Information</div>
                <div class="info-grid">
                    <div class="info-row">
                        <span class="info-label">Document ID</span>
                        <span class="info-value"><%= @document&.id || 'doc_a7b3c9f2-1e8d-4b4c-8f9a-5d6e7f8g9h0i' %></span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Reference Number</span>
                        <span class="info-value">GQXBE-QUYB4-BOWXD-BVTFS</span>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Signature Details</div>
                <div class="signature-section clearfix">
                    <div class="signature-details">
                        <div class="info-row-full">
                            <span class="info-label">Email Address</span>
                            <span class="info-value"><%= @patient&.email || '<EMAIL>' %></span>
                        </div>
                        <div class="info-row-full">
                            <span class="info-label">IP Address</span>
                            <span class="info-value"><%= @signature_request&.ip_address || '*************' %></span>
                        </div>
                        <div class="info-row-full">
                            <span class="info-label">Location</span>
                            <span class="info-value"><%= @patient&.city || 'Enfield' %>, <%= @patient&.country || 'United Kingdom' %></span>
                        </div>
                    </div>
                    <div class="signature-box">
                        <% if @signature_request&.signature_image&.attached? %>
                            <img alt="Digital Signature" class="signature-image" src="data:image/png;base64,<%= Base64.strict_encode64(@signature_request.signature_image.download) %>">
                        <% else %>
                            <div class="signature-text"><%= @signature_request&.signed_name || 'Sample Signature' %></div>
                        <% end %>
                    </div>
                </div>
            </div>
            <div class="verification-section">
                <div class="section-title">Verification Methods</div>
                <div class="verification-content">
                    <div class="verification-list">
                        <div class="verification-item">
                            The document was sent to <strong>Testing Practitioner</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            A secure 6 digit code was sent to <strong>+447772705073</strong> on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            The code was entered into the <strong>Your Practice</strong> patient portal on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            Patient logged in via mobile 2 factor authentication on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            The Document was viewed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            The document was signed on: <%= (@signature_request&.completed_at || Time.current).strftime('%d %b %Y %H:%M:%S UTC') %>
                        </div>
                        <div class="verification-item">
                            Copy of document was emailed to <strong><%= @patient&.email || '<EMAIL>' %></strong>
                        </div>
                        <div class="verification-item">
                            Link to document was sent via SMS to <strong>+447772705073</strong>
                        </div>
                    </div>

                    <div class="audit-trail-section">
                        <div class="device-title">Audit Trail - Device Information</div>
                        <div class="info-grid">
                            <div class="info-row">
                                <span class="info-label">Operating System</span>
                                <span class="info-value"><%= @signature_request&.os_name || 'macOS' %> <%= @signature_request&.os_version || 'Sonoma 14.5' %></span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Browser</span>
                                <span class="info-value"><%= @signature_request&.browser_name || 'Chrome' %> <%= @signature_request&.browser_version || '126.0.6478.127' %></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer clearfix">
            <div class="footer-left">UPOD MEDICAL</div>
            <div class="footer-right">
                <div class="footer-text">
                    This certificate confirms the successful completion and digital signing of the associated document.
                </div>
                <div class="footer-confirm">
                    Signature confirmed and verified using UPOD Medical practice management software
                </div>
            </div>
        </div>
    </div>
</body>
</html>
